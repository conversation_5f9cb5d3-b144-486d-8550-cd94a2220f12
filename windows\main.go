package windows

import (
	"bsphp/dnfspeed"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// MainWindow 主窗口结构体
type MainWindow struct {
	app             fyne.App
	window          fyne.Window
	speedController *dnfspeed.SpeedController
	statusLabel     *widget.Label
	speedEntry      *widget.Entry
	startBtn        *widget.Button
	stopBtn         *widget.Button
}

// NewMainWindow 创建新的主窗口
func NewMainWindow(a fyne.App) *MainWindow {
	w := a.NewWindow("DNF变速器")
	w.<PERSON><PERSON>ze(fyne.NewSize(600, 500))

	mw := &MainWindow{
		app:             a,
		window:          w,
		speedController: dnfspeed.NewSpeedController(),
		statusLabel:     widget.NewLabel("DNF变速器状态: 未启动"),
		speedEntry:      widget.NewEntry(),
	}

	// 创建按钮
	mw.startBtn = widget.NewButton("开始变速", mw.onStartSpeed)
	mw.stopBtn = widget.NewButton("停止变速", mw.onStopSpeed)
	mw.stopBtn.Disable() // 初始状态禁用

	// 设置速度输入框
	mw.speedEntry.SetPlaceHolder("输入变速速率(1-4)")
	mw.speedEntry.SetText("2") // 默认速率

	// 设置主窗口内容
	mw.setupContent()

	// 启动状态监听
	go mw.listenForStatus()

	return mw
}

// 设置主窗口内容
func (mw *MainWindow) setupContent() {
	// 设置描述区
	desc := widget.NewCard("DNF变速器", "一个简单易用的DNF游戏变速工具",
		widget.NewRichTextFromMarkdown("**功能**: 支持1-4倍速度调节\n**兼容**: 支持DNF游戏进程检测"))

	// DNF变速器区域
	dnfSpeedCard := mw.createDNFSpeedCard()

	// 直接使用DNF变速器内容
	content := container.NewBorder(
		desc, nil,
		nil, nil,
		dnfSpeedCard,
	)

	mw.window.SetContent(content)
}

// createDNFSpeedCard 创建DNF变速器卡片
func (mw *MainWindow) createDNFSpeedCard() *fyne.Container {
	// 速率设置区域
	speedLabel := widget.NewLabel("变速速率:")
	speedInfo := widget.NewLabel("(1=正常速度, 2=2倍速, 3=3倍速, 4=4倍速)")
	speedContainer := container.NewHBox(speedLabel, mw.speedEntry, speedInfo)

	// 控制按钮
	buttonContainer := container.NewHBox(mw.startBtn, mw.stopBtn)

	// 状态显示区域
	statusContainer := container.NewVBox(
		mw.statusLabel,
		widget.NewSeparator(),
	)

	// 使用说明
	instructions := widget.NewRichTextFromMarkdown(`
## 使用说明


1. **设置变速速率**: 在输入框中输入1-4的数字
   - 1: 正常速度
   - 2: 2倍速度
   - 3: 3倍速度
   - 4: 4倍速度

2. **开始变速**: 点击"开始变速"按钮，程序会自动等待DNF进程启动

3. **停止变速**: 点击"停止变速"按钮停止监控

4. **注意事项**:
   - 请确保以管理员权限运行程序
   - 变速功能仅在DNF游戏运行时生效
   - 程序会自动检测并修改1号和2号进程
`)

	// 组合所有元素
	content := container.NewVBox(
		speedContainer,
		widget.NewSeparator(),
		buttonContainer,
		widget.NewSeparator(),
		statusContainer,
		widget.NewSeparator(),
		instructions,
	)

	return container.NewPadded(content)
}

// onStartSpeed 开始变速按钮事件
func (mw *MainWindow) onStartSpeed() {
	speedStr := mw.speedEntry.Text
	speed, err := dnfspeed.ValidateSpeed(speedStr)
	if err != nil {
		dialog.NewError(err, mw.window).Show()
		return
	}

	err = mw.speedController.SetSpeed(speed)
	if err != nil {
		dialog.NewError(err, mw.window).Show()
		return
	}

	err = mw.speedController.Start()
	if err != nil {
		dialog.NewError(err, mw.window).Show()
		return
	}

	// 更新UI状态
	mw.updateUIForRunning(true)
	mw.statusLabel.SetText("DNF变速器状态: 正在启动...")
}

// onStopSpeed 停止变速按钮事件
func (mw *MainWindow) onStopSpeed() {
	mw.speedController.Stop()
	mw.updateUIForRunning(false)
	mw.statusLabel.SetText("DNF变速器状态: 已停止")
}

// updateUIForRunning 更新UI运行状态
func (mw *MainWindow) updateUIForRunning(running bool) {
	if running {
		mw.startBtn.Disable()
		mw.stopBtn.Enable()
		mw.speedEntry.Disable()
	} else {
		mw.startBtn.Enable()
		mw.stopBtn.Disable()
		mw.speedEntry.Enable()
	}
}

// listenForStatus 监听状态更新
func (mw *MainWindow) listenForStatus() {
	for status := range mw.speedController.GetStatusChan() {
		// 在主线程中更新UI
		mw.statusLabel.SetText("DNF变速器状态: " + status)

		// 检查是否完成或出错，更新按钮状态
		if strings.Contains(status, "完成") || strings.Contains(status, "失败") || strings.Contains(status, "错误") {
			mw.updateUIForRunning(false)
		}

		time.Sleep(100 * time.Millisecond) // 避免过于频繁的更新
	}
}

// 显示主窗口
func (mw *MainWindow) Show() {
	// 设置窗口关闭回调
	mw.window.SetCloseIntercept(func() {
		// 停止变速控制器
		if mw.speedController.IsRunning() {
			mw.speedController.Stop()
		}
		mw.window.Close()
	})

	mw.window.Show()
}
