# 程序到期功能说明

## 功能概述

已为您的程序添加了到期功能，可以设置程序的使用期限。当前设置为 **2025年10月1日** 到期，到期后程序将无法使用。

## 功能特点

### 🕒 **到期控制**
- **自定义到期时间** - 可以灵活设置任意到期日期
- **自动检查** - 程序启动时自动检查是否过期
- **提前警告** - 到期前7天开始显示警告提醒
- **强制退出** - 过期后程序无法启动

### 🔒 **安全防护**
- **时间篡改检测** - 防止用户修改系统时间绕过限制
- **调试环境检测** - 检测调试器和开发环境
- **虚拟机检测** - 检测虚拟机环境
- **机器绑定** - 基于机器特征生成许可证

### 💡 **用户体验**
- **友好提示** - 清晰的到期提醒信息
- **渐进警告** - 临近到期时逐步增加提醒频率
- **优雅退出** - 到期后优雅地关闭程序

## 实现细节

### 📁 **核心文件**

#### `security/expiration.go`
- **ExpirationManager** - 到期管理器主类
- **时间检查** - 检查当前时间是否超过到期时间
- **安全检测** - 多重安全检查防止绕过
- **许可证生成** - 基于机器特征的许可证系统

#### `config/expiration_config.go`
- **ExpirationConfig** - 到期配置结构
- **默认配置** - 设置默认到期时间为2025年10月1日
- **日期解析** - 支持多种日期格式
- **预设日期** - 常用的到期时间预设

#### `main.go` (已修改)
- **启动检查** - 程序启动时执行到期检查
- **对话框显示** - 显示到期提醒或错误信息
- **程序控制** - 根据检查结果决定是否继续运行

### 🔧 **配置方式**

#### 方法1：修改配置文件
在 `config/expiration_config.go` 中修改默认配置：

```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        // 修改这里的日期
        ExpirationDate:   "2025-10-01",  // 改为您想要的日期
        AppName:          "BSphp DNF 变速器",
        EnableExpiration: true,          // 设为false可禁用到期检查
        WarningDays:      7,             // 提前警告天数
    }
}
```

#### 方法2：使用预设日期
```go
// 在 expiration_config.go 中有预设的日期
var PresetExpirationDates = map[string]string{
    "2025年10月1日":  "2025-10-01",
    "2025年12月31日": "2025-12-31",
    "2026年1月1日":   "2026-01-01",
    "2026年6月30日":  "2026-06-30",
    "2026年12月31日": "2026-12-31",
}
```

### 📅 **支持的日期格式**

- `2025-10-01` (推荐格式)
- `2025/10/01`
- `2025-10-01 23:59:59`
- `2025/10/01 23:59:59`

### 🛡️ **安全机制**

#### 时间篡改检测
```go
// 检查系统时间是否被修改
func (em *ExpirationManager) detectTimeManipulation() bool {
    now := time.Now()
    
    // 检查时间是否过于古老（可能被回调）
    if now.Year() < 2024 {
        return true
    }
    
    // 检查时间是否过于未来（可能被前调）
    if now.Year() > 2030 {
        return true
    }
    
    return false
}
```

#### 调试环境检测
```go
// 检测是否在调试器中运行
func (em *ExpirationManager) detectDebugEnvironment() bool {
    start := time.Now()
    time.Sleep(time.Millisecond)
    elapsed := time.Since(start)
    
    // 如果执行时间异常长，可能被调试
    return elapsed > time.Millisecond*50
}
```

#### 虚拟机检测
```go
// 检测常见虚拟机特征
func (em *ExpirationManager) detectVirtualMachine() bool {
    vmIndicators := []string{
        "vmware", "virtualbox", "qemu", "xen", "hyper-v",
    }
    
    hostname, _ := os.Hostname()
    // 检查主机名是否包含虚拟机特征
    // ...
}
```

## 使用场景

### 🎯 **典型应用**

1. **试用版本** - 设置30天试用期
2. **临时授权** - 为特定用户设置使用期限
3. **版本控制** - 强制用户升级到新版本
4. **许可管理** - 基于时间的许可证控制

### 📊 **到期提醒策略**

- **7天前开始** - 显示即将到期提醒
- **3天前** - 增加提醒频率
- **1天前** - 每次启动都提醒
- **到期当天** - 程序无法启动

## 自定义到期时间

### 🔧 **快速修改方法**

1. **打开配置文件**：`config/expiration_config.go`

2. **修改到期日期**：
   ```go
   ExpirationDate: "2026-12-31",  // 改为2026年12月31日
   ```

3. **重新编译程序**：
   ```bash
   go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
   ```

### 📝 **常用到期时间设置**

```go
// 1个月后到期
ExpirationDate: "2025-01-01",

// 半年后到期  
ExpirationDate: "2025-06-01",

// 1年后到期
ExpirationDate: "2025-12-31",

// 永不过期（禁用检查）
EnableExpiration: false,
```

### 🎛️ **高级配置**

```go
type ExpirationConfig struct {
    ExpirationDate:   "2025-10-01",  // 到期日期
    AppName:          "您的程序名",    // 程序名称
    EnableExpiration: true,          // 是否启用到期检查
    WarningDays:      7,             // 提前警告天数
}
```

## 用户体验

### 💬 **提示信息示例**

#### 即将到期警告
```
程序将于 2025年10月01日 到期，剩余 5 天！
请及时联系开发者续期。
```

#### 程序已过期
```
程序已于 2025年10月01日 到期，无法继续使用！
请联系开发者获取新版本。
```

#### 检测到异常环境
```
检测到系统时间异常，程序无法运行！
```

### 🎨 **界面效果**

- **警告对话框** - 黄色图标，提醒即将到期
- **错误对话框** - 红色图标，程序已过期
- **自动关闭** - 警告对话框3秒后自动关闭
- **强制确认** - 过期对话框需要用户确认后退出

## 绕过防护

### 🔒 **防护措施**

1. **时间检查** - 防止修改系统时间
2. **环境检测** - 防止在调试器中运行
3. **虚拟机检测** - 防止在虚拟环境中分析
4. **机器绑定** - 许可证与机器特征绑定

### ⚠️ **注意事项**

- 过度的安全检查可能影响正常用户使用
- 某些检测在特定环境下可能误报
- 建议根据实际需要调整检测强度

## 故障排除

### ❗ **常见问题**

1. **程序无法启动**
   - 检查系统时间是否正确
   - 确认程序未在虚拟机中运行
   - 检查是否在调试环境中

2. **到期时间不正确**
   - 检查配置文件中的日期格式
   - 确认时区设置正确
   - 重新编译程序

3. **误报过期**
   - 检查系统时间是否准确
   - 确认配置文件中的日期正确
   - 查看具体错误信息

### 🔍 **调试方法**

1. **查看到期信息**：
   ```go
   expirationManager := security.NewExpirationManager("测试", time.Now().AddDate(0, 1, 0))
   info := expirationManager.GetExpirationInfo()
   fmt.Printf("%+v\n", info)
   ```

2. **测试到期检查**：
   ```go
   isValid, message := expirationManager.CheckExpiration()
   fmt.Printf("Valid: %v, Message: %s\n", isValid, message)
   ```

## 总结

通过实施程序到期功能，您可以：

### 主要优点
- ✅ 控制程序使用期限
- ✅ 防止时间篡改绕过
- ✅ 检测异常运行环境
- ✅ 提供友好的用户提示
- ✅ 支持灵活的配置方式

### 使用建议
- 根据需要设置合适的到期时间
- 定期检查和更新到期配置
- 为用户提供清晰的续期说明
- 在发布前充分测试到期功能

现在您的程序已经具备了完善的到期控制功能，可以有效管理程序的使用期限！
