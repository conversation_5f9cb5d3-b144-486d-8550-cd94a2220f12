@echo off

echo BSphp Release Build Script
echo ============================

rem Check if icon file exists
if not exist "icon.ico" (
    echo Error: icon.ico not found
    echo Please make sure icon.ico is in the current directory
    pause
    exit /b 1
)

rem Check if rsrc tool is available
rsrc -h >nul 2>&1
if errorlevel 1 (
    echo Error: rsrc tool not found
    echo Installing rsrc...
    go install github.com/akavel/rsrc@latest
    if errorlevel 1 (
        echo Failed to install rsrc
        pause
        exit /b 1
    )
    echo rsrc installed successfully
)

echo.
echo [1/4] Generating icon resource...
rsrc -ico icon.ico -o app.syso
if errorlevel 1 (
    echo Failed to generate icon resource
    pause
    exit /b 1
)
echo Icon resource generated successfully

echo [2/4] Cleaning old files...
if exist "bsphp.exe" del "bsphp.exe"
echo Cleanup completed

echo [3/4] Building program (optimized)...
go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
if errorlevel 1 (
    echo Build failed
    if exist "app.syso" del "app.syso"
    pause
    exit /b 1
)
echo Build successful

echo [4/4] Cleaning temporary files...
if exist "app.syso" del "app.syso"
echo Cleanup completed

echo.
echo ============================
echo Build Completed Successfully!
echo ============================

if exist "bsphp.exe" (
    for %%A in (bsphp.exe) do (
        echo File: bsphp.exe
        echo Size: %%~zA bytes
        echo Date: %%~tA
    )
    echo.
    echo Build successful! Program is ready.
    echo.
    
    set /p run_choice="Run program now? (Y/N): "
    if /i "%run_choice%"=="Y" (
        echo.
        echo Starting program...
        start "" "bsphp.exe"
    )
) else (
    echo Build failed: Output file not found
)

echo.
echo Press any key to exit...
pause >nul
