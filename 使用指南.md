# BSphp DNF变速器 使用指南

## 快速开始

### 1. 编译程序
```bash
# 方法一：使用批处理脚本
双击运行 build.bat

# 方法二：手动编译
go mod tidy
go build -o bsphp-dnf.exe
```

### 2. 运行程序
```bash
# 方法一：使用管理员启动脚本（推荐）
双击运行 run_as_admin.bat

# 方法二：手动以管理员身份运行
右键点击 bsphp-dnf.exe -> 以管理员身份运行
```

## 详细使用步骤

### 第一步：登录验证
1. 启动程序后会显示登录窗口
2. 输入您的账号和密码
3. 如果需要验证码，请输入验证码
4. 点击登录按钮

### 第二步：进入主界面
1. 登录成功后自动跳转到主界面
2. 主界面直接显示DNF变速器功能

### 第三步：使用DNF变速器
1. 在 **"变速速率"** 输入框中输入速率值（1-4）：
   - `1`：正常速度
   - `2`：2倍速度
   - `3`：3倍速度
   - `4`：4倍速度
2. 点击 **"开始变速"** 按钮
3. 程序会显示状态：**"正在等待游戏1号进程启动..."**
4. 启动DNF游戏
5. 程序检测到游戏进程后会自动修改内存
6. 等待2号进程启动并自动处理
7. 完成后显示：**"速率修改完成！"**

### 第四步：停止变速（可选）
- 随时点击 **"停止变速"** 按钮停止监控
- 关闭程序时会自动停止变速功能

## 功能说明

### 变速原理
- 通过修改游戏进程内存中的时间计算函数实现变速
- 使用特征码搜索定位目标内存地址
- 安全的内存修改，不会损坏游戏文件

### 支持特性
- **双进程支持**：自动处理DNF的1号和2号进程
- **实时状态**：显示当前操作进度和状态
- **安全停止**：支持随时中断操作
- **错误处理**：完善的错误提示和处理

### 状态说明
- **"未启动"**：变速器尚未开始工作
- **"正在启动..."**：变速器正在初始化
- **"正在等待游戏X号进程启动..."**：等待游戏进程
- **"已检测到游戏X号进程"**：成功找到游戏进程
- **"正在为X号进程设置速率"**：正在修改内存
- **"X号进程速率修改成功！"**：内存修改完成
- **"速率修改完成！"**：所有操作完成
- **"已停止"**：用户手动停止或程序结束

## 注意事项

### 系统要求
- **操作系统**：Windows 7/8/10/11
- **权限要求**：必须以管理员身份运行
- **游戏版本**：支持当前主流DNF版本

### 使用建议
1. **合理使用**：请遵守游戏规则，合理使用变速功能
2. **备份存档**：使用前建议备份游戏存档
3. **关闭杀毒**：部分杀毒软件可能误报，请添加信任
4. **网络稳定**：确保网络连接稳定，避免掉线

### 常见问题

#### Q: 程序提示"权限不足"
A: 请确保以管理员身份运行程序，可以使用 `run_as_admin.bat` 脚本

#### Q: 找不到游戏进程
A: 请确保：
- DNF游戏已正常启动
- 游戏进程名为 "DNF.exe"
- 没有被其他程序阻止

#### Q: 内存修改失败
A: 可能原因：
- 游戏版本不兼容
- 杀毒软件阻止
- 系统权限不足

#### Q: 变速不生效
A: 请检查：
- 是否成功修改了所有进程
- 游戏是否重新启动
- 速率设置是否正确

## 技术支持

### 日志查看
程序运行时会在状态栏显示详细信息，如遇问题请记录相关状态信息。

### 联系方式
如遇技术问题，请通过原有的意见反馈渠道联系技术支持。

### 版本更新
程序会通过原有的版本检查机制提示更新，请及时更新到最新版本。

## 免责声明

本软件仅供学习和研究使用，使用者应遵守相关法律法规和游戏规则。因使用本软件造成的任何后果，开发者不承担任何责任。请合理使用，享受游戏乐趣。
