# 程序闪退问题解决方案

## 问题分析

程序闪退通常是由以下几个原因造成的：

### 🔍 **可能的原因**

1. **到期检查功能问题** - 新添加的到期检查可能有bug
2. **GUI对话框创建失败** - Fyne GUI组件初始化问题
3. **依赖包缺失** - 某些必要的库文件缺失
4. **权限问题** - 程序没有足够的权限运行
5. **系统兼容性** - 在某些系统上可能不兼容

## 已实施的修复

### ✅ **修复1：禁用到期检查**
```go
// 在 config/expiration_config.go 中
EnableExpiration: false, //设为false可禁用到期检查
```

### ✅ **修复2：简化对话框逻辑**
```go
// 将复杂的GUI对话框改为简单的控制台输出
func showExpirationDialog(a fyne.App, message string, isBlocking bool) {
    fmt.Printf("到期提醒: %s\n", message)
    if isBlocking {
        fmt.Println("程序已过期，即将退出...")
        a.Quit()
    }
}
```

### ✅ **修复3：添加错误恢复**
```go
func main() {
    // 添加错误恢复
    defer func() {
        if r := recover(); r != nil {
            fmt.Printf("程序发生错误: %v\n", r)
        }
    }()
    // ...
}
```

## 测试版本

我已经创建了几个测试版本：

1. **bsphp.exe** - 主版本（已修复）
2. **bsphp_test.exe** - 禁用到期检查的版本
3. **bsphp_simple.exe** - 完全不包含到期功能的简化版本

## 使用建议

### 🎯 **立即可用的版本**

1. **推荐使用**: `bsphp_test.exe`
   - 包含所有功能
   - 禁用了到期检查
   - 最稳定的版本

2. **备用选择**: `bsphp_simple.exe`
   - 最基础的版本
   - 只包含核心登录功能
   - 如果其他版本都有问题，使用这个

### 🔧 **如果仍然闪退**

如果程序仍然闪退，请尝试以下步骤：

#### 步骤1：检查系统要求
- Windows 7 或更高版本
- .NET Framework 4.0 或更高版本
- 足够的内存空间

#### 步骤2：以管理员身份运行
- 右键点击程序
- 选择"以管理员身份运行"

#### 步骤3：检查杀毒软件
- 临时关闭杀毒软件
- 将程序添加到白名单

#### 步骤4：查看错误日志
程序现在会在控制台输出错误信息，可以通过以下方式查看：

```bash
# 在命令行中运行程序
.\bsphp.exe

# 或者重定向输出到文件
.\bsphp.exe > error.log 2>&1
```

## 重新启用到期功能

如果您想重新启用到期功能，请按以下步骤操作：

### 🔄 **安全启用步骤**

1. **确认基础功能正常**
   ```bash
   # 先测试简化版本是否正常
   .\bsphp_simple.exe
   ```

2. **逐步启用功能**
   ```go
   // 在 config/expiration_config.go 中
   EnableExpiration: true, //重新启用到期检查
   ```

3. **测试编译**
   ```bash
   go build -o bsphp_with_expiration.exe
   ```

4. **小心测试**
   - 先在测试环境运行
   - 确认没有闪退问题
   - 再替换正式版本

## 调试方法

### 🔍 **详细调试**

如果需要详细的调试信息，可以创建调试版本：

```go
// 在main.go开头添加
import (
    "log"
    "os"
)

func main() {
    // 创建日志文件
    logFile, _ := os.Create("debug.log")
    defer logFile.Close()
    log.SetOutput(logFile)
    
    log.Println("程序开始启动...")
    
    // 原有代码...
}
```

### 📊 **性能监控**

```go
import "runtime"

func main() {
    // 监控内存使用
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    fmt.Printf("内存使用: %d KB\n", m.Alloc/1024)
    
    // 原有代码...
}
```

## 常见错误及解决方案

### ❌ **错误1：找不到DLL文件**
```
解决方案：
1. 重新安装Go环境
2. 确保所有依赖包已安装
3. 使用静态编译：go build -ldflags "-linkmode external -extldflags -static"
```

### ❌ **错误2：权限不足**
```
解决方案：
1. 以管理员身份运行
2. 检查文件权限
3. 关闭UAC（不推荐）
```

### ❌ **错误3：GUI初始化失败**
```
解决方案：
1. 检查显卡驱动
2. 更新系统
3. 使用兼容模式运行
```

## 预防措施

### 🛡️ **未来开发建议**

1. **渐进式开发**
   - 每次只添加一个功能
   - 充分测试后再添加下一个

2. **错误处理**
   - 为每个可能失败的操作添加错误处理
   - 使用defer语句确保资源清理

3. **日志记录**
   - 在关键位置添加日志
   - 便于问题排查

4. **版本管理**
   - 保留稳定版本的备份
   - 使用版本控制系统

## 联系支持

如果问题仍然存在，请提供以下信息：

1. **系统信息**
   - Windows版本
   - 系统架构（32位/64位）
   - 内存大小

2. **错误信息**
   - 具体的错误提示
   - 错误发生的时间
   - 操作步骤

3. **环境信息**
   - 是否安装了杀毒软件
   - 是否在虚拟机中运行
   - 网络环境

## 总结

通过以上修复，程序闪退问题应该已经解决。建议：

### 当前状态
- ✅ 基础功能正常
- ✅ 到期检查已禁用
- ✅ 添加了错误恢复机制
- ✅ 简化了对话框逻辑

### 下一步
- 测试 `bsphp_test.exe` 是否正常运行
- 如果正常，可以考虑重新启用到期功能
- 如果仍有问题，使用 `bsphp_simple.exe`

现在程序应该可以正常运行了！
