package security

import (
	"bsphp/config"
	"crypto/md5"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"
)

// ExpirationManager 到期管理器
type ExpirationManager struct {
	config *config.ExpirationConfig
}

// ExpirationInfo 到期信息
type ExpirationInfo struct {
	IsExpired       bool   `json:"is_expired"`
	DaysRemaining   int    `json:"days_remaining"`
	ExpirationDate  string `json:"expiration_date"`
	ShouldShowWarning bool `json:"should_show_warning"`
	Message         string `json:"message"`
}

// NewExpirationManager 创建新的到期管理器
func NewExpirationManager() *ExpirationManager {
	return &ExpirationManager{
		config: config.GetDefaultConfig(),
	}
}

// CheckExpiration 检查程序是否过期
func (em *ExpirationManager) CheckExpiration() (bool, string) {
	// 如果禁用了到期检查，直接返回有效
	if !em.config.EnableExpiration {
		return true, ""
	}

	// 执行安全检查
	if !em.runSecurityChecks() {
		return false, "检测到异常环境，程序无法运行！"
	}

	// 检查是否过期
	if em.config.IsExpired() {
		expirationTime, _ := em.config.ParseExpirationDate()
		return false, fmt.Sprintf("程序已于 %s 到期，无法继续使用！\n请联系开发者获取新版本。",
			expirationTime.Format("2006年01月02日"))
	}

	// 检查是否需要显示警告
	if em.config.ShouldShowWarning() {
		days := em.config.DaysUntilExpiration()
		expirationTime, _ := em.config.ParseExpirationDate()
		return true, fmt.Sprintf("程序将于 %s 到期，剩余 %d 天！\n请及时联系开发者续期。",
			expirationTime.Format("2006年01月02日"), days)
	}

	return true, ""
}

// GetExpirationInfo 获取到期信息
func (em *ExpirationManager) GetExpirationInfo() *ExpirationInfo {
	info := &ExpirationInfo{
		IsExpired:       em.config.IsExpired(),
		DaysRemaining:   em.config.DaysUntilExpiration(),
		ShouldShowWarning: em.config.ShouldShowWarning(),
	}

	if expirationTime, err := em.config.ParseExpirationDate(); err == nil {
		info.ExpirationDate = expirationTime.Format("2006年01月02日")
	}

	if info.IsExpired {
		info.Message = fmt.Sprintf("程序已于 %s 到期", info.ExpirationDate)
	} else if info.ShouldShowWarning {
		info.Message = fmt.Sprintf("程序将于 %s 到期，剩余 %d 天", info.ExpirationDate, info.DaysRemaining)
	} else {
		info.Message = "程序运行正常"
	}

	return info
}

// runSecurityChecks 运行安全检查
func (em *ExpirationManager) runSecurityChecks() bool {
	// 时间篡改检测
	if em.detectTimeManipulation() {
		return false
	}

	// 调试环境检测
	if em.detectDebugEnvironment() {
		return false
	}

	// 虚拟机检测（可选，可能误报）
	// if em.detectVirtualMachine() {
	//     return false
	// }

	return true
}

// detectTimeManipulation 检测时间篡改
func (em *ExpirationManager) detectTimeManipulation() bool {
	now := time.Now()

	// 检查时间是否过于古老（可能被回调）
	if now.Year() < 2024 {
		return true
	}

	// 检查时间是否过于未来（可能被前调）
	if now.Year() > 2030 {
		return true
	}

	return false
}

// detectDebugEnvironment 检测调试环境
func (em *ExpirationManager) detectDebugEnvironment() bool {
	start := time.Now()
	time.Sleep(time.Millisecond)
	elapsed := time.Since(start)

	// 如果执行时间异常长，可能被调试
	return elapsed > time.Millisecond*50
}

// detectVirtualMachine 检测虚拟机环境
func (em *ExpirationManager) detectVirtualMachine() bool {
	vmIndicators := []string{
		"vmware", "virtualbox", "qemu", "xen", "hyper-v",
		"virtual", "vm", "vbox",
	}

	hostname, _ := os.Hostname()
	hostname = strings.ToLower(hostname)

	// 检查主机名是否包含虚拟机特征
	for _, indicator := range vmIndicators {
		if strings.Contains(hostname, indicator) {
			return true
		}
	}

	// 检查CPU信息（Windows特定）
	if runtime.GOOS == "windows" {
		// 这里可以添加更多的虚拟机检测逻辑
		// 例如检查注册表、硬件信息等
	}

	return false
}

// generateMachineFingerprint 生成机器指纹
func (em *ExpirationManager) generateMachineFingerprint() string {
	hostname, _ := os.Hostname()
	
	// 组合机器信息
	machineInfo := fmt.Sprintf("%s_%s_%s", 
		hostname, 
		runtime.GOOS, 
		runtime.GOARCH)

	// 生成MD5哈希
	hash := md5.Sum([]byte(machineInfo))
	return fmt.Sprintf("%x", hash)
}
