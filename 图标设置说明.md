# 程序图标设置说明

## 功能概述

已成功为您的程序设置了自定义图标，编译后的 `bsphp.exe` 将使用项目文件夹中的 `icon.ico` 作为程序图标。

## 实现方法

### 🔧 **技术方案**

使用 `rsrc` 工具将 ICO 图标文件嵌入到 Go 程序中：

1. **安装 rsrc 工具**：
   ```bash
   go install github.com/akavel/rsrc@latest
   ```

2. **生成资源文件**：
   ```bash
   rsrc -ico icon.ico -o app.syso
   ```

3. **编译程序**：
   ```bash
   go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
   ```

### 📁 **相关文件**

- `icon.ico` - 您的自定义图标文件
- `app.syso` - 生成的 Windows 资源文件（临时文件）
- `app.rc` - Windows 资源脚本文件（备用方案）

## 编译脚本更新

### 🚀 **secure_build.bat 增强**

已更新 `secure_build.bat` 脚本，现在会自动处理图标嵌入：

```batch
:: 检查并生成图标资源
if exist "icon.ico" (
    echo [信息] 找到图标文件，生成资源文件...
    rsrc -ico icon.ico -o app.syso
    if errorlevel 1 (
        echo [警告] 图标资源生成失败，继续编译
    ) else (
        echo [信息] 图标资源生成成功
    )
) else (
    echo [警告] 未找到 icon.ico 文件，跳过图标嵌入
)
```

### ✨ **新增功能**

1. **自动检测图标**：脚本会自动检查 `icon.ico` 文件是否存在
2. **智能处理**：如果图标文件不存在，会跳过图标嵌入但继续编译
3. **状态提示**：显示图标处理的详细状态信息
4. **自动清理**：编译完成后自动删除临时的 `app.syso` 文件

## 使用方法

### 📋 **步骤说明**

1. **准备图标文件**：
   - 确保项目根目录有 `icon.ico` 文件
   - 图标文件应该是标准的 ICO 格式
   - 建议包含多种尺寸（16x16, 32x32, 48x48, 256x256）

2. **编译程序**：
   ```bash
   # 方法1：使用增强的安全编译脚本
   .\secure_build.bat
   
   # 方法2：手动编译
   rsrc -ico icon.ico -o app.syso
   go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
   ```

3. **验证结果**：
   - 编译完成后，`bsphp.exe` 应该显示您的自定义图标
   - 在文件资源管理器中可以看到图标
   - 程序运行时任务栏也会显示该图标

## 图标要求

### 📐 **格式规范**

- **文件格式**：ICO (Windows Icon)
- **推荐尺寸**：
  - 16x16 像素（小图标）
  - 32x32 像素（标准图标）
  - 48x48 像素（大图标）
  - 256x256 像素（高清图标）
- **颜色深度**：32位（支持透明度）

### 🎨 **设计建议**

- 图标应该简洁明了，易于识别
- 在小尺寸下仍然清晰可见
- 使用适当的对比度
- 考虑在不同背景下的显示效果

## 故障排除

### ❗ **常见问题**

1. **rsrc 命令未找到**：
   ```bash
   # 解决方案：安装 rsrc 工具
   go install github.com/akavel/rsrc@latest
   ```

2. **图标不显示**：
   - 检查 `icon.ico` 文件是否存在
   - 确认 ICO 文件格式正确
   - 重新编译程序

3. **编译失败**：
   - 检查 Go 环境是否正常
   - 确认所有依赖包已安装
   - 查看具体错误信息

### 🔍 **调试方法**

1. **检查资源文件**：
   ```bash
   # 查看是否生成了 app.syso
   dir app.syso
   ```

2. **验证图标文件**：
   - 使用图像查看器打开 `icon.ico`
   - 确认文件没有损坏

3. **手动测试**：
   ```bash
   # 手动生成资源文件
   rsrc -ico icon.ico -o test.syso
   
   # 检查是否成功
   dir test.syso
   ```

## 高级配置

### 🛠️ **自定义资源脚本**

如果需要更复杂的资源配置，可以使用 `app.rc` 文件：

```rc
// app.rc - Windows 资源文件
#include <windows.h>

// 应用程序图标
IDI_ICON1 ICON "icon.ico"

// 版本信息
VS_VERSION_INFO VERSIONINFO
FILEVERSION     1,0,0,0
PRODUCTVERSION  1,0,0,0
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904E4"
        BEGIN
            VALUE "CompanyName",      "BSphp"
            VALUE "FileDescription",  "BSphp DNF 变速器"
            VALUE "FileVersion",      "*******"
            VALUE "ProductName",      "BSphp DNF 变速器"
        END
    END
END
```

### 🔧 **使用 windres 编译**

如果安装了 MinGW，也可以使用 windres：

```bash
# 编译资源文件
windres -o app.syso app.rc

# 编译程序
go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
```

## 效果展示

### ✅ **成功标志**

编译成功后，您应该看到：

1. **文件资源管理器**：`bsphp.exe` 显示自定义图标
2. **任务栏**：程序运行时显示自定义图标
3. **桌面快捷方式**：如果创建快捷方式，也会显示自定义图标
4. **编译日志**：显示 "✓ 自定义图标 (icon.ico)" 信息

### 📊 **编译输出示例**

```
[信息] 找到图标文件，生成资源文件...
[信息] 图标资源生成成功
[信息] 开始安全编译...
[信息] garble混淆编译成功
[信息] UPX压缩成功

================================
        编译完成！
================================
[输出文件] bsphp.exe
[安全特性] 
  ✓ 去除调试信息和符号表
  ✓ 隐藏控制台窗口
  ✓ 自定义图标 (icon.ico)
  ✓ 代码混淆 (garble)
  ✓ 文件压缩 (UPX)
```

## 总结

通过使用 `rsrc` 工具和更新编译脚本，现在您的程序可以：

### 主要优点
- ✅ 自动嵌入自定义图标
- ✅ 编译脚本智能处理
- ✅ 支持标准 ICO 格式
- ✅ 自动清理临时文件
- ✅ 完整的错误处理

### 使用建议
- 保持 `icon.ico` 文件在项目根目录
- 使用高质量的 ICO 文件
- 定期检查图标显示效果
- 如有问题及时查看编译日志

现在您的 `bsphp.exe` 程序将使用您提供的 `icon.ico` 作为程序图标！
