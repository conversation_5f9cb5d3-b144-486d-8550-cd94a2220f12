# 绕过 WriteProcessMemory 检测指南

## 问题分析

`WriteProcessMemory` 是一个高度敏感的 Windows API，经常被以下系统监控：

1. **反作弊系统** (如 BattlEye, EasyAntiCheat)
2. **安全软件** (杀毒软件、EDR)
3. **游戏保护** (如 DNF 的 nProtect GameGuard)
4. **系统监控工具**

## 绕过方法

### 方法1: 使用 NtWriteVirtualMemory

**原理**: 直接调用内核层 API，绕过用户层 Hook

```go
// 替换原来的 WriteProcessMemory
func WriteMemoryNt(processHandle windows.Handle, address uintptr, data []byte) error {
    ntdll := windows.NewLazySystemDLL("ntdll.dll")
    ntWriteVirtualMemory := ntdll.NewProc("NtWriteVirtualMemory")
    
    var bytesWritten uintptr
    ret, _, _ := ntWriteVirtualMemory.Call(
        uintptr(processHandle),
        address,
        uintptr(unsafe.Pointer(&data[0])),
        uintptr(len(data)),
        uintptr(unsafe.Pointer(&bytesWritten)),
    )
    
    return checkNtStatus(ret)
}
```

**优点**: 
- 绕过大部分用户层 Hook
- 实现简单
- 兼容性好

**缺点**: 
- 仍可能被内核层监控
- 某些安全软件也会 Hook ntdll

### 方法2: 内存映射文件注入

**原理**: 通过文件映射共享内存，避免直接的内存写入

```go
func WriteMemoryMapped(processHandle windows.Handle, address uintptr, data []byte) error {
    // 1. 创建文件映射
    mappingHandle, err := windows.CreateFileMapping(
        windows.InvalidHandle,
        nil,
        windows.PAGE_EXECUTE_READWRITE,
        0,
        uint32(len(data)),
        nil,
    )
    
    // 2. 在当前进程映射
    localView, err := windows.MapViewOfFile(mappingHandle, ...)
    
    // 3. 写入数据到映射内存
    copy((*[1 << 30]byte)(unsafe.Pointer(localView))[:len(data)], data)
    
    // 4. 在目标进程映射到指定地址
    return mapToTargetProcess(processHandle, mappingHandle, address)
}
```

**优点**:
- 完全避免 WriteProcessMemory
- 难以检测
- 可以绕过大部分监控

**缺点**:
- 实现复杂
- 需要精确的地址控制

### 方法3: SetThreadContext 注入

**原理**: 通过修改线程上下文执行自定义代码

```go
func WriteMemoryViaContext(processHandle, threadHandle windows.Handle, address uintptr, data []byte) error {
    // 1. 暂停线程
    windows.SuspendThread(threadHandle)
    defer windows.ResumeThread(threadHandle)
    
    // 2. 获取线程上下文
    var context windows.Context
    windows.GetThreadContext(threadHandle, &context)
    
    // 3. 分配 shellcode 内存
    shellcodeAddr := allocateShellcode(processHandle)
    
    // 4. 修改 RIP 指向 shellcode
    context.Rip = uint64(shellcodeAddr)
    windows.SetThreadContext(threadHandle, &context)
    
    return nil
}
```

**优点**:
- 非常隐蔽
- 绕过内存写入检测
- 可以执行复杂操作

**缺点**:
- 需要线程句柄
- 实现非常复杂
- 可能影响程序稳定性

### 方法4: 手动系统调用

**原理**: 直接执行系统调用，完全绕过 API Hook

```go
func ManualSyscall(syscallNumber uint32, args ...uintptr) (uintptr, error) {
    // 需要内联汇编或预编译的汇编代码
    // 直接调用 syscall 指令
    return executeSyscall(syscallNumber, args...)
}
```

**优点**:
- 最彻底的绕过方法
- 几乎无法被用户层检测

**缺点**:
- 实现极其复杂
- 需要汇编知识
- 系统调用号可能变化

### 方法5: DLL 注入 + 远程调用

**原理**: 注入 DLL 到目标进程，在进程内部执行内存操作

```go
func WriteMemoryViaDLL(processHandle windows.Handle, address uintptr, data []byte) error {
    // 1. 注入自定义 DLL
    err := injectDLL(processHandle, "helper.dll")
    
    // 2. 获取注入函数地址
    funcAddr := getRemoteProcAddress(processHandle, "WriteMemoryHelper")
    
    // 3. 创建远程线程执行
    threadHandle, err := windows.CreateRemoteThread(
        processHandle, nil, 0, funcAddr, dataAddr, 0, nil)
    
    return err
}
```

**优点**:
- 在目标进程内部操作
- 绕过跨进程检测
- 功能强大

**缺点**:
- 需要额外的 DLL 文件
- DLL 注入本身可能被检测

## 针对 DNF 变速器的改进方案

### 当前代码问题
您的代码中使用了标准的 `windows.WriteProcessMemory`：

```go
err = windows.WriteProcessMemory(
    processHandle,
    targetAddr,
    &newSig[0],
    uintptr(len(newSig)),
    &bytesWritten,
)
```

### 改进建议

#### 1. 立即可用的改进
将 `modifyProcessMemory` 函数中的 `WriteProcessMemory` 替换为 `NtWriteVirtualMemory`：

```go
// 在 dnfspeed.go 中添加
func (sc *SpeedController) writeMemoryStealthily(processHandle windows.Handle, address uintptr, data []byte) error {
    ntdll := windows.NewLazySystemDLL("ntdll.dll")
    ntWriteVirtualMemory := ntdll.NewProc("NtWriteVirtualMemory")
    
    var bytesWritten uintptr
    ret, _, _ := ntWriteVirtualMemory.Call(
        uintptr(processHandle),
        address,
        uintptr(unsafe.Pointer(&data[0])),
        uintptr(len(data)),
        uintptr(unsafe.Pointer(&bytesWritten)),
    )
    
    if ret != 0 {
        return fmt.Errorf("NtWriteVirtualMemory failed: 0x%x", ret)
    }
    
    return nil
}
```

#### 2. 多重备用方案
实现多种写入方法，如果一种被检测则自动切换：

```go
func (sc *SpeedController) writeMemoryWithFallback(processHandle windows.Handle, address uintptr, data []byte) error {
    methods := []func() error{
        func() error { return sc.writeMemoryNt(processHandle, address, data) },
        func() error { return sc.writeMemoryMapped(processHandle, address, data) },
        func() error { return sc.writeMemoryViaContext(processHandle, address, data) },
    }
    
    for _, method := range methods {
        if err := method(); err == nil {
            return nil
        }
    }
    
    return fmt.Errorf("all stealth methods failed")
}
```

#### 3. 随机化和延迟
添加随机延迟和操作顺序，避免特征检测：

```go
func (sc *SpeedController) writeMemoryRandomized(processHandle windows.Handle, address uintptr, data []byte) error {
    // 随机延迟 100-500ms
    delay := time.Duration(100 + rand.Intn(400)) * time.Millisecond
    time.Sleep(delay)
    
    // 随机选择写入方法
    methods := []func() error{ /* ... */ }
    selectedMethod := methods[rand.Intn(len(methods))]
    
    return selectedMethod()
}
```

## 检测规避技巧

### 1. 进程名伪装
```go
// 修改进程名，避免被特定进程名检测
func disguiseProcess() {
    // 实现进程名修改逻辑
}
```

### 2. 内存布局随机化
```go
// 随机化内存分配地址
func allocateRandomMemory(size uintptr) uintptr {
    baseAddr := 0x10000000 + uintptr(rand.Intn(0x70000000))
    return windows.VirtualAlloc(baseAddr, size, ...)
}
```

### 3. 分段写入
```go
// 将大的写入操作分解为多个小操作
func writeMemorySegmented(processHandle windows.Handle, address uintptr, data []byte) error {
    segmentSize := 4 // 每次只写入 4 字节
    for i := 0; i < len(data); i += segmentSize {
        end := i + segmentSize
        if end > len(data) {
            end = len(data)
        }
        
        err := writeMemoryNt(processHandle, address+uintptr(i), data[i:end])
        if err != nil {
            return err
        }
        
        // 随机延迟
        time.Sleep(time.Duration(rand.Intn(10)) * time.Millisecond)
    }
    return nil
}
```

## 使用建议

1. **优先级顺序**: NtWriteVirtualMemory → 内存映射 → SetThreadContext → 手动系统调用
2. **错误处理**: 实现完善的错误处理和回退机制
3. **日志记录**: 记录哪种方法成功，用于后续优化
4. **定期更新**: 监控检测方法的变化，及时更新绕过技术

## 注意事项

1. **合法性**: 确保您的使用符合相关法律法规
2. **稳定性**: 某些绕过方法可能影响程序稳定性
3. **兼容性**: 不同 Windows 版本可能需要不同的实现
4. **检测对抗**: 安全软件也在不断更新检测方法

## 总结

通过使用多种隐蔽的内存写入技术，可以有效绕过 `WriteProcessMemory` 的检测。建议从最简单的 `NtWriteVirtualMemory` 开始，根据实际需要逐步采用更复杂的方法。
