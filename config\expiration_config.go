package config

import (
	"time"
)

// ExpirationConfig 到期配置结构
type ExpirationConfig struct {
	ExpirationDate   string `json:"expiration_date"`   // 到期日期
	AppName          string `json:"app_name"`          // 程序名称
	EnableExpiration bool   `json:"enable_expiration"` // 是否启用到期检查
	WarningDays      int    `json:"warning_days"`      // 提前警告天数
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *ExpirationConfig {
	return &ExpirationConfig{
		ExpirationDate:   "2025-10-01",        // 默认到期时间：2025年10月1日
		AppName:          "BSphp DNF 变速器",    // 程序名称
		EnableExpiration: true,                // 默认启用到期检查
		WarningDays:      7,                   // 提前7天警告
	}
}

// PresetExpirationDates 预设的到期日期
var PresetExpirationDates = map[string]string{
	"2025年10月1日":  "2025-10-01",
	"2025年12月31日": "2025-12-31",
	"2026年1月1日":   "2026-01-01",
	"2026年6月30日":  "2026-06-30",
	"2026年12月31日": "2026-12-31",
}

// ParseExpirationDate 解析到期日期
func (c *ExpirationConfig) ParseExpirationDate() (time.Time, error) {
	// 支持多种日期格式
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, c.ExpirationDate); err == nil {
			// 如果只有日期没有时间，设置为当天的23:59:59
			if format == "2006-01-02" || format == "2006/01/02" {
				return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location()), nil
			}
			return t, nil
		}
	}

	return time.Time{}, &time.ParseError{
		Layout: "2006-01-02",
		Value:  c.ExpirationDate,
	}
}

// IsExpired 检查是否已过期
func (c *ExpirationConfig) IsExpired() bool {
	if !c.EnableExpiration {
		return false
	}

	expirationTime, err := c.ParseExpirationDate()
	if err != nil {
		return true // 如果日期格式错误，认为已过期
	}

	return time.Now().After(expirationTime)
}

// DaysUntilExpiration 计算距离到期还有多少天
func (c *ExpirationConfig) DaysUntilExpiration() int {
	if !c.EnableExpiration {
		return -1 // 未启用到期检查
	}

	expirationTime, err := c.ParseExpirationDate()
	if err != nil {
		return 0 // 如果日期格式错误，返回0天
	}

	duration := expirationTime.Sub(time.Now())
	days := int(duration.Hours() / 24)

	if days < 0 {
		return 0 // 已过期
	}

	return days
}

// ShouldShowWarning 检查是否应该显示警告
func (c *ExpirationConfig) ShouldShowWarning() bool {
	if !c.EnableExpiration {
		return false
	}

	days := c.DaysUntilExpiration()
	return days <= c.WarningDays && days > 0
}
