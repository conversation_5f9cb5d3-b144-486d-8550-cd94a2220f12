package widgets

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

type Spacer struct {
	widget.BaseWidget
	width  float32
	height float32
}

func NewSpacerX(width float32) *Spacer {
	return &Spacer{
		width: width,
	}
}
func NewSpacerY(height float32) *Spacer {
	return &Spacer{
		height: height,
	}
}
func NewSpacer(width, height float32) *Spacer {
	return &Spacer{
		width:  width,
		height: height,
	}
}

func (s *Spacer) CreateRenderer() fyne.WidgetRenderer {
	spacer := container.NewCenter()
	spacer.Resize(fyne.NewSize(s.width, s.height))
	return widget.NewSimpleRenderer(spacer)
}
