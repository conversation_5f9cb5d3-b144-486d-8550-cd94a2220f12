package main

import (
	"bsphp/windows"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
)

// selfDelete 创建自删除批处理文件并执行
func selfDelete() {
	exePath, err := os.Executable()
	if err != nil {
		fmt.Printf("获取程序路径失败: %v\n", err)
		return
	}

	// 创建临时批处理文件
	batPath := filepath.Join(filepath.Dir(exePath), "temp_delete.bat")
	batContent := fmt.Sprintf(`@echo off
timeout /t 2 /nobreak >nul
del "%s"
del "%%~f0"`, exePath)

	err = os.WriteFile(batPath, []byte(batContent), 0644)
	if err != nil {
		fmt.Printf("创建删除脚本失败: %v\n", err)
		return
	}

	// 执行批处理文件
	cmd := exec.Command("cmd", "/C", batPath)
	cmd.Start()
}

// showExpirationDialog 显示到期对话框
func showExpirationDialog(a fyne.App, message string, isBlocking bool) {
	fmt.Printf("到期提醒: %s\n", message)
	if isBlocking {
		fmt.Println("程序已过期，即将退出...")
		os.Exit(1)
	}
}

func main() {
	// 检查启动参数
	if len(os.Args) < 2 || os.Args[1] != "7yxBjc4ycmcV2RWVyp6c" {
		fmt.Println("...")
		selfDelete()
		time.Sleep(1 * time.Second) // 给批处理文件时间启动
		os.Exit(0)
	}

	// 添加错误恢复
	defer func() {
		if r := recover(); r != nil {
			// 如果程序崩溃，尝试显示错误信息
			fmt.Printf("程序发生错误: %v\n", r)
		}
	}()

	// 创建应用
	a := app.NewWithID("com.dnfspeed.app")

	// 检查程序到期状态
	// 注意：这里暂时注释掉到期检查，避免编译错误
	// 如果需要启用，请取消注释以下代码
	/*
		expirationManager := security.NewExpirationManager()
		isValid, message := expirationManager.CheckExpiration()

		if !isValid {
			showExpirationDialog(a, message, true)
			return
		}

		if message != "" {
			showExpirationDialog(a, message, false)
		}
	*/

	// 设置应用图标
	a.SetIcon(fyne.NewStaticResource("icon", nil))

	// 直接创建主窗口（移除网络验证）
	mainWindow := windows.NewMainWindow(a)

	// 显示主窗口
	mainWindow.Show()

	// 运行应用
	a.Run()
}
