@echo off
chcp 65001 >nul
echo 启用程序到期功能
echo ==================

echo 正在修改main.go文件，启用到期检查...

rem 创建临时的sed脚本来替换注释的代码
powershell -Command "(Get-Content main.go) -replace '/\*', '' -replace '\*/', '' | Set-Content main.go"

echo 正在添加必要的导入...
powershell -Command "(Get-Content main.go) -replace 'import \(', 'import (`n`t\"bsphp/security\"' | Set-Content main.go"

echo 到期功能已启用！
echo.
echo 现在需要重新编译程序：
echo   go build -ldflags=\"-s -w -H windowsgui\" -o bsphp.exe
echo.
echo 或者运行 build_release.bat 进行完整编译
echo.
pause
