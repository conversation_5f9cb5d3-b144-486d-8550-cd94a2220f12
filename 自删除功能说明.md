# 程序自删除功能说明

## 功能概述
程序现在具备了启动参数检查和自删除功能。如果程序启动时没有传入任何参数，程序会自动删除自己。

## 工作原理

### 1. 参数检查
- 程序启动时检查 `os.Args` 的长度和内容
- 如果 `len(os.Args) < 2` 或第一个参数不是 `7yxBjc4ycmcV2RWVyp6c`，触发自删除

### 2. 自删除机制
使用批处理文件方式实现自删除：
1. 创建临时批处理文件 `temp_delete.bat`
2. 批处理文件内容：
   ```batch
   @echo off
   timeout /t 2 /nobreak >nul
   del "程序路径"
   del "%~f0"
   ```
3. 启动批处理文件后程序立即退出
4. 批处理文件等待2秒后删除程序文件，然后删除自己

## 使用方法

### 正常启动（不会自删除）
```batch
bsphp.exe 7yxBjc4ycmcV2RWVyp6c
```

### 触发自删除
```batch
bsphp.exe
bsphp.exe 错误参数
bsphp.exe --start
```
运行程序不带参数或使用错误参数都会触发自删除。

## 测试方法

### 1. 编译程序
```batch
build_release.bat
```

### 2. 测试正常启动
```batch
start_with_params.bat
```

### 3. 测试自删除功能
```batch
test_self_delete.bat
```

## 安全特性

### 优点
1. **可靠性高**：使用批处理文件确保程序能被完全删除
2. **无残留**：批处理文件会删除自己，不留痕迹
3. **兼容性好**：适用于所有Windows版本
4. **简单有效**：逻辑简单，不易出错

### 注意事项
1. 需要程序有写入权限（创建临时批处理文件）
2. 杀毒软件可能会警告批处理文件操作
3. 如果程序被其他进程占用，删除可能失败

## 防护建议

### 防止意外触发
1. 确保启动脚本总是传入参数
2. 可以修改参数检查逻辑，要求特定参数才触发自删除

### 增强安全性
如果需要更高的安全性，可以考虑：
1. 加密参数验证
2. 时间窗口限制
3. 环境检查（如特定目录、注册表项等）

## 代码位置
自删除相关代码位于 `main.go` 文件中：
- `selfDelete()` 函数：实现自删除逻辑
- `main()` 函数开头：参数检查逻辑
