# 启用程序到期功能说明

## 🎯 功能概述

程序到期功能已经完全实现，但默认处于注释状态。您可以按照以下步骤启用此功能。

## 📁 已创建的文件

### 1. 配置文件
- `config/expiration_config.go` - 到期配置管理
- 默认到期时间：2025年10月1日
- 默认启用状态：true

### 2. 安全模块
- `security/expiration.go` - 到期检查和安全验证
- 包含时间篡改检测、调试环境检测等

### 3. 主程序集成
- `main.go` - 已添加到期检查逻辑（注释状态）

## 🔧 启用步骤

### 方法1：手动启用（推荐）

#### 步骤1：修改导入
在 `main.go` 文件的导入部分添加：
```go
import (
	"bsphp/security"  // 添加这一行
	"bsphp/windows"
	"fmt"
	// ... 其他导入
)
```

#### 步骤2：取消注释到期检查代码
在 `main.go` 的 main 函数中，找到以下注释的代码：
```go
/*
expirationManager := security.NewExpirationManager()
isValid, message := expirationManager.CheckExpiration()

if !isValid {
	showExpirationDialog(a, message, true)
	return
}

if message != "" {
	showExpirationDialog(a, message, false)
}
*/
```

将其修改为：
```go
expirationManager := security.NewExpirationManager()
isValid, message := expirationManager.CheckExpiration()

if !isValid {
	showExpirationDialog(a, message, true)
	return
}

if message != "" {
	showExpirationDialog(a, message, false)
}
```

#### 步骤3：重新编译
```bash
go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
```

### 方法2：使用批处理脚本
运行 `enable_expiration.bat` 脚本自动启用（可能需要手动调整）

## ⚙️ 配置到期时间

### 修改到期日期
编辑 `config/expiration_config.go` 文件：
```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        ExpirationDate:   "2025-10-01",        // 修改这里的日期
        AppName:          "BSphp DNF 变速器",
        EnableExpiration: true,                // 确保为true
        WarningDays:      7,                   // 提前警告天数
    }
}
```

### 支持的日期格式
- `"2025-10-01"` (推荐)
- `"2025/10/01"`
- `"2025-10-01 23:59:59"`

### 常用设置示例
```go
// 30天试用版
ExpirationDate: "2025-01-31",
WarningDays: 3,

// 1年授权版
ExpirationDate: "2025-12-31", 
WarningDays: 7,

// 禁用到期检查
EnableExpiration: false,
```

## 🛡️ 安全特性

### 已实现的保护
1. **时间篡改检测** - 防止修改系统时间绕过
2. **调试环境检测** - 检测调试器运行
3. **参数验证** - 需要正确的启动参数
4. **自删除功能** - 无参数时自动删除

### 可选的额外保护
- 虚拟机检测（已实现但默认禁用，可能误报）
- 机器指纹绑定
- 网络时间验证

## 📋 测试方法

### 1. 测试正常运行
```bash
bsphp.exe 7yxBjc4ycmcV2RWVyp6c
```

### 2. 测试到期提醒
临时修改配置文件中的日期为明天，测试警告功能

### 3. 测试过期阻止
临时修改配置文件中的日期为昨天，测试阻止功能

## 🔍 故障排除

### 编译错误
如果出现编译错误，检查：
1. 导入是否正确添加
2. 注释是否完全移除
3. 语法是否正确

### 运行时错误
如果程序无法启动：
1. 检查配置文件中的日期格式
2. 确认启动参数正确
3. 查看控制台错误信息

### 禁用到期功能
如果需要临时禁用：
```go
// 在 config/expiration_config.go 中
EnableExpiration: false,
```

## 📝 注意事项

1. **修改后需要重新编译** - 配置是编译时确定的
2. **测试后再发布** - 确保功能正常工作
3. **备份原文件** - 修改前备份main.go
4. **用户体验** - 合理设置警告时间，避免突然中断

## 🚀 快速启用命令

```bash
# 1. 手动编辑main.go文件（添加导入和取消注释）
# 2. 重新编译
go build -ldflags="-s -w -H windowsgui" -o bsphp.exe
# 3. 测试运行
bsphp.exe 7yxBjc4ycmcV2RWVyp6c
```

启用后，程序将具备完整的到期控制功能！
